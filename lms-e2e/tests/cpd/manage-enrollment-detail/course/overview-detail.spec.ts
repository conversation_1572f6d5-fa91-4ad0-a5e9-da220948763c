import { expect } from '@playwright/test';
import { v4 as uuidv4 } from 'uuid';
import { test } from '../../../../fixtures/default-fixture';
import { RoundsRepo } from '../../../../../shared/repositories/lms/repo/rounds-repo/round-repo';
import { UsersRepo } from '../../../../../shared/repositories/lms/repo/users-repo/users-repo';
import { EnrollmentsRepo } from '../../../../../shared/repositories/lms/repo/enrollments-repo/enrollments-repo';
import { EnrollmentCertificatesRepo } from '../../../../../shared/repositories/lms/repo/enrollments-repo/enrollment-certificates-repo';
import { EnrollmentAttachmentsRepo } from '../../../../../shared/repositories/lms/repo/enrollments-repo/enrollment-attachments-repo';
import { QuizAnswersRepo } from '../../../../../shared/repositories/lms/repo/enrollments-repo/quiz-answers-repo';
import { PreEnrollmentTransactionRepo } from '../../../../../shared/repositories/lms/repo/pre-enrollments-repo/pre-enrollment-transaction-repo';
import { EnrollType } from '../../../../../shared/repositories/lms/constants/enums/enroll-type.enum';
import { CertificatePage } from '../../../../elements/pages/regular-site/certificate-page';

async function cleanUpRoundAndEnrollment(
  repositories: {
    roundRepo: RoundsRepo;
    usersRepo: UsersRepo;
    enrollmentsRepo: EnrollmentsRepo;
    enrollmentCertificatesRepo: EnrollmentCertificatesRepo;
    enrollmentAttachmentsRepo: EnrollmentAttachmentsRepo;
    quizAnswersRepo: QuizAnswersRepo;
    preEnrollmentTransactionRepo: PreEnrollmentTransactionRepo;
  },
  email: string,
  roundDate: Date,
  course: string,
) {
  const lmsUser = await repositories.usersRepo.getByEmail(email);
  const userEnrollments = await repositories.enrollmentsRepo.getAllEnrollmentsForUser(lmsUser.guid);

  // Clean up round
  await repositories.roundRepo.deleteTrainingRoundByTrainingDateAndCourseId(course, roundDate);

  // Clean up enrollment
  for (const userEnrollment of userEnrollments) {
    await repositories.enrollmentCertificatesRepo.deleteAllEnrollmentCertificatesForEnrollment(userEnrollment.id);
    await repositories.enrollmentAttachmentsRepo.deleteAllEnrollmentAttachedForEnrollment(userEnrollment.id);
    await repositories.quizAnswersRepo.deleteManyByEnrollmentId(userEnrollment.id);
  }
  await repositories.enrollmentsRepo.deleteAllEnrollmentsForUser(lmsUser.guid);

  // Clean Pre-Enrollment Transaction
  await repositories.preEnrollmentTransactionRepo.deleteByPayloadEmail(lmsUser.email);

  // Clean user return URL
  await repositories.usersRepo.clearUserReturnUrl(lmsUser.email);
}

test.beforeEach(
  async ({
    roundRepo,
    configuration,
    usersRepo,
    enrollmentsRepo,
    enrollmentCertificatesRepo,
    enrollmentAttachmentsRepo,
    quizAnswersRepo,
    preEnrollmentTransactionRepo,
  }) => {
    const courses = [configuration.shareCourses.courseOIC4DeductionForCPD];
    const lmsUsers = [configuration.shareUsers.userSendDeduction];

    const currentDate = new Date();
    const roundDate = new Date(new Date().setDate(currentDate.getDate() + 2));
    roundDate.setHours(0, 0, 0, 0);

    // Clean data enrollment and round
    for (const course of courses) {
      for (const user of lmsUsers) {
        await cleanUpRoundAndEnrollment(
          {
            roundRepo,
            usersRepo,
            enrollmentsRepo,
            enrollmentCertificatesRepo,
            enrollmentAttachmentsRepo,
            quizAnswersRepo,
            preEnrollmentTransactionRepo,
          },
          user.email,
          roundDate,
          course.courseId,
        );
      }
    }
  },
);

test.afterEach(
  async ({
    roundRepo,
    configuration,
    usersRepo,
    enrollmentsRepo,
    enrollmentCertificatesRepo,
    enrollmentAttachmentsRepo,
    quizAnswersRepo,
    preEnrollmentTransactionRepo,
  }) => {
    const courses = [configuration.shareCourses.courseOIC4DeductionForCPD];
    const lmsUsers = [configuration.shareUsers.userSendDeduction];
    const currentDate = new Date();
    const roundDate = new Date(new Date().setDate(currentDate.getDate() + 2));
    roundDate.setHours(0, 0, 0, 0);

    for (const course of courses) {
      for (const user of lmsUsers) {
        await cleanUpRoundAndEnrollment(
          {
            roundRepo,
            usersRepo,
            enrollmentsRepo,
            enrollmentCertificatesRepo,
            enrollmentAttachmentsRepo,
            quizAnswersRepo,
            preEnrollmentTransactionRepo,
          },
          user.email,
          roundDate,
          course.courseId,
        );
      }
    }
  },
);

test.describe('Admin - Manage enrollment course detail', () => {
  test('SKL-T20037 SKL-T20038 Admin verify multiple certificates when approve enrollment and able to re-generate certificates', async ({
    loginCpdPage,
    page,
    configuration,
    homePage,
    myCoursePage,
    learningPage,
    adminDashboardPage,
    verifyIdentityPage,
    pendingApprovalPage,
    pendingApprovalDetailPage,
    enrollmentsRepo,
    enrollmentCertificatesRepo,
    roundRepo,
    testmailAppClient,
  }) => {
    const course = configuration.shareCourses.courseOIC4DeductionForCPD;
    const courseVersion = course.courseVersions[1];
    const certificates = courseVersion.certificates;

    const learnerCPD = configuration.shareUsers.userSendDeduction;
    const adminCPD = configuration.shareUsers.adminRequestDeduction;

    // Seed round
    const date = new Date();
    const expiredAt = new Date();
    expiredAt.setMonth(expiredAt.getMonth() + 1);

    const roundDate = new Date();
    const firstRegisDate = new Date(new Date().setMonth(roundDate.getMonth() - 1));
    const lastRegisDate = new Date(new Date().setMonth(roundDate.getMonth() + 1));
    roundDate.setHours(0, 0, 0, 0);
    firstRegisDate.setHours(9, 0, 0, 0);
    lastRegisDate.setHours(16, 59, 0, 0);

    const round = await roundRepo.create({
      id: uuidv4(),
      roundDate: roundDate,
      firstRegistrationDate: firstRegisDate,
      lastRegistrationDate: lastRegisDate,
      createdAt: date,
      updatedAt: date,
      courseIds: [course.courseId],
      organizationId: learnerCPD.organizationId,
    });

    // Seed enrollment
    const enrollment = await enrollmentsRepo.create({
      id: uuidv4(),
      courseId: course.courseId,
      courseVersionId: courseVersion.id,
      organizationId: learnerCPD.organizationId,
      userId: learnerCPD.guid,
      business: EnrollmentsRepo.BUSINESS.b2c,
      status: EnrollmentsRepo.STATUS.inProgress,
      isCountdownArticle: false,
      roundId: round.id,
      completedCourseItem: 0,
      learningProgress: [],
      startedAt: date,
      expiredAt: expiredAt,
      acceptedAt: null,
      createdAt: date,
      updatedAt: date,
      isIdentityVerificationEnabled: false,
      remark: '',
      externalContentType: '',
      enrollType: EnrollType.voluntary,
      customerCode: '',
    });

    // seed enrollment certificate
    for (const certificate of certificates) {
      await enrollmentCertificatesRepo.create({
        id: uuidv4(),
        courseVersionCertificateId: certificate.id,
        enrollmentId: enrollment.id,
        slugName: certificate.slugName,
        payload: {
          logoImageUrl: '',
          tsiCode: '',
          issuedBy: '',
          pillarName: '',
        },
        pillarName: '',
        refCode: certificate.refCode,
        refName: certificate.refName,
        tsiCode: '',
        type: certificate.type,
        certificateUrl: '',
        certificateCode: '',
        logoImageUrl: '',
        issuedBy: '',
        isSentEmailOnExpiredDate: false,
        isSentEmail: false,
        createdAt: date,
        updatedAt: date,
      });
    }

    // Learner login
    await loginCpdPage.accessLoginSSO();
    await loginCpdPage.fillUsername(learnerCPD.username);
    await loginCpdPage.fillPassword(learnerCPD.password);
    await loginCpdPage.submitButton();
    await expect(homePage.userProfileLocator).toBeVisible();

    // Learner continue learning
    await myCoursePage.clickToMyCourse();
    await myCoursePage.waitForCourseLoaded();
    await myCoursePage.continueLearning(1);
    await verifyIdentityPage.submitAgreeConsentCPD();
    await learningPage.nextButtonLocator.click();
    await learningPage.finishOICCourseModalLocator.waitFor({ state: 'visible' });
    await learningPage.submitLearningResultOnModal();
    await myCoursePage.waitForCourseLoaded();
    await page.waitForLoadState('load');
    expect(await myCoursePage.verifyCourseStatusByCourseName(course.name)).toContain('รออนุมัติ');
    await homePage.logout();

    // Admin login
    await loginCpdPage.accessLoginSSO();
    await loginCpdPage.fillUsername(adminCPD.username);
    await loginCpdPage.fillPassword(adminCPD.password);
    await loginCpdPage.submitButton();
    await expect(homePage.userProfileLocator).toBeVisible();

    // Admin verify enrollment
    await adminDashboardPage.accessManagePendingApprovalOIC();
    await pendingApprovalPage.searchPendingApproval(`${learnerCPD.firstname} ${learnerCPD.lastname}`);
    await pendingApprovalDetailPage.accessPendingApprovalDetail();
    await pendingApprovalDetailPage.pendingApprovalElement.submitVerified();
    await expect(pendingApprovalDetailPage.successEditEnrollmentApprovalToastMsgLocator).toBeVisible();
    await pendingApprovalDetailPage.approvalHistoryTab.click();
    await expect(pendingApprovalDetailPage.latestApprovalHistoryStatusLocator).toContainText('ผ่านการตรวจสอบ');

    // Admin approve enrollment
    await pendingApprovalDetailPage.pendingApprovalElement.submitApproval();
    await expect(pendingApprovalDetailPage.successEditEnrollmentApprovalToastMsgLocator).toBeVisible();
    await expect(pendingApprovalDetailPage.latestApprovalHistoryStatusLocator).toContainText('อนุมัติ');

    // refresh page
    await page.reload();
    await expect(pendingApprovalDetailPage.verifyMultiCertificateButtonLocator).toBeVisible();

    // Handle new tab for non-life certificate verification
    const [_NonLifeCertificatePage] = await Promise.all([
      page.context().waitForEvent('page'),
      pendingApprovalDetailPage.verifyMultiCertificateButtonLocator.click(),
      pendingApprovalDetailPage.multiCertNonLifeOptionLocator.click(),
    ]);

    await _NonLifeCertificatePage.waitForLoadState();

    // Verify certificate content
    const nonLifeCertificatePage = new CertificatePage(_NonLifeCertificatePage);
    await nonLifeCertificatePage.verifyCertificateContent(
      learnerCPD.salute,
      learnerCPD.firstname,
      learnerCPD.lastname,
      course.name,
    );
    await _NonLifeCertificatePage.close();

    // Handle new tab for life certificate verification
    const [LifeCertificatePage] = await Promise.all([
      page.context().waitForEvent('page'),
      pendingApprovalDetailPage.verifyMultiCertificateButtonLocator.click(),
      pendingApprovalDetailPage.multiCertLifeOptionLocator.click(),
    ]);

    await LifeCertificatePage.waitForLoadState();

    // Verify certificate content
    const lifeCertificatePage = new CertificatePage(LifeCertificatePage);
    await lifeCertificatePage.verifyCertificateContent(
      learnerCPD.salute,
      learnerCPD.firstname,
      learnerCPD.lastname,
      course.name,
    );

    await LifeCertificatePage.close();

    const timestamp = new Date().getTime();
    // re-gen certificate
    // non-life
    await pendingApprovalDetailPage.moreOptionButtonLocator.click();
    await pendingApprovalDetailPage.reGenerateCertificateButtonLocator.click();
    await pendingApprovalDetailPage.reGenerateMultiCertNonLifeOptionLocator.click();
    await expect(pendingApprovalDetailPage.toastSuccessReGenerateCertificateAndSendEmailLocator).toBeVisible();

    // life
    await pendingApprovalDetailPage.moreOptionButtonLocator.click();
    await pendingApprovalDetailPage.reGenerateCertificateButtonLocator.click();
    await pendingApprovalDetailPage.reGenerateMultiCertLifeOptionLocator.click();
    await expect(pendingApprovalDetailPage.toastSuccessReGenerateCertificateAndSendEmailLocator).toBeVisible();

    // verify re-generate certificate email
    for (const certificate of certificates) {
      const email = await testmailAppClient.fetchLatestEmailInbox(
        learnerCPD.email,
        `*ประกาศนียบัตร${certificate.refName}*`,
        timestamp,
      );
      expect(email.html).toContain(courseVersion.name);
      expect(email.html).toContain(certificate.refName);
    }
  });
});
