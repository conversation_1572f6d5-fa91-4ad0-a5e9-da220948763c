import { expect } from '@playwright/test';
import { test } from '../../../fixtures/default-fixture';

test.beforeEach(async ({}) => {});
test.afterEach(async ({}) => {});

test.describe('Admin - Course Management', () => {
  test('@SKL-T20189 Admin เข้าถึงหน้าหลักสูตรทั้งหมด, แสดงหน้าตารางรายการหลักสูตรทั้งหมด', async ({
    configuration,
    manageCourseListPage,
    adminDashboardPage,
    loginPage,
    homePage,
  }) => {
    const organizationAdmin = configuration.usersLocal.organizationAdminTLI1;

    // Admin login
    await loginPage.loginWithUsernameAndPassword(organizationAdmin.username, organizationAdmin.password);
    await expect(homePage.userProfileLocator).toBeVisible();

    await adminDashboardPage.accessCoursesMenu();

    await expect(manageCourseListPage.courseRowsLocator.first()).toBeVisible();
    await expect(manageCourseListPage.searchInputLocator).toBeVisible();
    await expect(manageCourseListPage.searchSubmitBtnLocator).toBeVisible();
    await expect(manageCourseListPage.createCourseButtonLocator).toBeVisible();
  });
});
