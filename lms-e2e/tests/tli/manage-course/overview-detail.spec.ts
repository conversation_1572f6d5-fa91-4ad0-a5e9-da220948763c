import { expect } from '@playwright/test';
import { test } from '../../../fixtures/default-fixture';
import { ObjectiveCourse } from '../../../elements/pages/backoffice/create-course-elements';
import { ApplicantType, LicenseRenewal } from '../../../elements/pages/backoffice/general-course-detail-elements';
import { CourseStatus, MediaType } from '../../../elements/pages/backoffice/curriculum/manage-curriculum-page';

test.beforeEach(async ({}) => {});
test.afterEach(async ({}) => {});

test.describe('Admin - Course Management', () => {
  test('@SKL-T20189 Admin เข้าถึงหน้าหลักสูตรทั้งหมด, แสดงหน้าตารางรายการหลักสูตรทั้งหมด', async ({
    configuration,
    loginCpdPage,
    manageCourseListPage,
    adminDashboardPage,
    homePage,
  }) => {
    const adminCPD = configuration.shareUsers.adminRequestDeduction;

    // Admin login
    await loginCpdPage.accessLoginSSO();
    await loginCpdPage.fillUsername(adminCPD.username);
    await loginCpdPage.fillPassword(adminCPD.password);
    await loginCpdPage.submitButton();
    await expect(homePage.userProfileLocator).toBeVisible();

    await adminDashboardPage.accessCoursesMenu();
    await expect(manageCourseListPage.courseRowsLocator.first()).toBeVisible();
    await expect(manageCourseListPage.searchInputLocator).toBeVisible();
    await expect(manageCourseListPage.searchSubmitBtnLocator).toBeVisible();
    await expect(manageCourseListPage.createCourseButtonLocator).toBeVisible();
  });
});
