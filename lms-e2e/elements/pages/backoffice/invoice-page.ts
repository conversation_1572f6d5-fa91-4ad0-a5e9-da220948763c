import { Locator, Page, expect } from '@playwright/test';
import { BasePage } from '../base-page';

export class InvoicePage extends BasePage {
  readonly requestRefundButtonLocator: Locator = this.page.getByRole('button', { name: 'ขอคืนเครดิต' });
  readonly confirmButtonLocator: Locator = this.page.getByRole('button', { name: 'ยืนยัน' });
  readonly toastMsgLocator = this.page.locator('div.ant-message-notice-content');

  constructor(page: Page) {
    super(page, '/admin/customers*');
  }

  async refundCredit(): Promise<this> {
    await expect(this.requestRefundButtonLocator).toBeVisible();
    await this.requestRefundButtonLocator.click();
    await expect(this.confirmButtonLocator).toBeVisible();
    await this.confirmButtonLocator.click();

    return this;
  }

  async successRefundCredit(): Promise<this> {
    await expect(this.toastMsgLocator).toContainText('ขอคืนเครดิตสำเร็จ');

    return this;
  }
}
