import { CustomerSaleOrderItemsModel } from '../../models/credits-models/customer-sale-order-item';

export class CustomerSaleOrderItemsRepo {
  readonly mongoose: typeof import('mongoose');

  constructor(mongoose: typeof import('mongoose')) {
    this.mongoose = mongoose;
  }

  async deleteByCustomerCode(customerCode: string) {
    return CustomerSaleOrderItemsModel.deleteMany({ customerCode: customerCode });
  }

  async getByCustomerCode(customerCode: string) {
    return CustomerSaleOrderItemsModel.find({ customerCode: customerCode });
  }
}
