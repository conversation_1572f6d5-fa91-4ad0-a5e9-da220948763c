import { model, ObjectId, Schema } from 'mongoose';

export interface CustomerSaleOrderItems {
  _id: ObjectId;
  id: string;
  customerCode: string;
  purchaseOrderNo: string;
  point: number;
  remainPoint: number;
  price: number;
  startedAt: Date;
  expiredAt: Date;
  deletedAt: Date;
  createdAt: Date;
  updatedAt: Date;
}

const schema = new Schema<CustomerSaleOrderItems>(
  {
    id: { type: Schema.Types.String },
    customerCode: { type: Schema.Types.String },
    purchaseOrderNo: { type: Schema.Types.String },
    point: { type: Schema.Types.Number },
    remainPoint: { type: Schema.Types.Number },
    price: { type: Schema.Types.Number },
    startedAt: { type: Schema.Types.Date },
    expiredAt: { type: Schema.Types.Date },
    deletedAt: { type: Schema.Types.Date },
    createdAt: { type: Schema.Types.Date },
    updatedAt: { type: Schema.Types.Date },
  },
  { versionKey: false },
);

export const CustomerSaleOrderItemsModel = model<CustomerSaleOrderItems>('customer-sale-order-items', schema);
