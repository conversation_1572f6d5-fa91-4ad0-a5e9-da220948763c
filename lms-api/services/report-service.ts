import { APIResponse } from '@playwright/test';
import { APIContextManager } from '../../shared/api-request/api-context-manager';
import { BaseAPIService } from '../../shared/api-request/base-api-service';
import { Configuration } from './configuration';

export class ReportService extends BaseAPIService {
  endpointRegularPreEnrollmentTransactions: string;
  endpointPreEnrollmentReservationsV2: string;
  endpointOrganizations: string;
  endpointPreEnrollmentReservations: string;
  endpointReportsV1: string;
  endpointExportOICRegulatorPreReport: string;
  endpointReportHistoriesV1: string;
  endpointReportHistories: string;
  endpointExportOICRegulatorPostReport: string;
  endpointExportLearnerReport: string;
  endpointOrganizationReportV1: string;
  endpointOrganizationReport: string;

  constructor(contextManager: APIContextManager, configuration: Configuration) {
    super(contextManager, configuration);
    this.endpoint = '/pre-enrollment-transactions-v2';
    this.endpointRegularPreEnrollmentTransactions = '/regular-pre-enrollment-transactions';
    this.endpointPreEnrollmentReservationsV2 = '/pre-enrollment-reservations-v2';
    this.endpointOrganizations = '/organizations';
    this.endpointPreEnrollmentReservations = '/pre-enrollment-reservations';
    this.endpointReportsV1 = '/reports-v1';
    this.endpointExportOICRegulatorPreReport = '/export-oic-regulator-pre-report';
    this.endpointReportHistoriesV1 = '/report-histories-v1';
    this.endpointReportHistories = '/report-histories';
    this.endpointExportOICRegulatorPostReport = '/export-oic-regulator-post-report';
    this.endpointExportLearnerReport = '/export-learner-report';
    this.endpointOrganizationReportV1 = '/organization-report-v1';
    this.endpointOrganizationReport = '/organization-reports';
  }

  async exportRegularPreEnrollmentTransactionReport(
    startDate: Date,
    endDate: Date,
    status: string,
    code?: string,
    email?: string,
    firstname?: string,
    withToken = true,
  ): Promise<APIResponse> {
    const resp = await this.contextManager
      .getContext()
      .post(`${this.baseApiUrl}${this.endpoint}/export-regular-pre-enrollment-transaction-report`, {
        headers: this.getHeaderForBaseUrl(withToken),
        data: {
          filters: {
            'pre_enrollment.courses': [
              {
                field: 'code',
                value: code,
              },
            ],
            'pre_enrollment.email': [
              {
                field: 'email',
                value: email,
              },
            ],
            'pre_enrollment.full_name': [
              {
                field: 'firstname',
                value: firstname,
              },
            ],
            'pre_enrollment.round_date': [
              {
                field: 'roundDate',
                value: [startDate, endDate],
              },
            ],
            'pre_enrollment.status': [
              {
                field: 'status',
                value: status,
              },
            ],
          },
          exportCfg: {
            columnSettings: [
              { key: 'pre_enrollment.full_name' },
              { key: 'pre_enrollment.email' },
              { key: 'pre_enrollment.courses' },
              { key: 'pre_enrollment.round_date' },
              { key: 'pre_enrollment.status' },
            ],
          },
        },
      });
    return resp;
  }

  async exportOICPreEnrollmentTransactionReport(
    startDate: Date,
    endDate: Date,
    status: string,
    code?: string,
    email?: string,
    firstname?: string,
    withToken = true,
  ): Promise<APIResponse> {
    const resp = await this.contextManager
      .getContext()
      .post(`${this.baseApiUrl}${this.endpoint}/export-pre-enrollment-transaction-report`, {
        headers: this.getHeaderForBaseUrl(withToken),
        data: {
          filters: {
            'pre_enrollment.courses': [
              {
                field: 'code',
                value: code,
              },
            ],
            'pre_enrollment.email': [
              {
                field: 'email',
                value: email,
              },
            ],
            'pre_enrollment.full_name': [
              {
                field: 'firstname',
                value: firstname,
              },
            ],
            'pre_enrollment.round_date': [
              {
                field: 'roundDate',
                value: [startDate, endDate],
              },
            ],
            'pre_enrollment.status': [
              {
                field: 'status',
                value: status,
              },
            ],
          },
          exportCfg: {
            columnSettings: [
              { key: 'pre_enrollment.full_name' },
              { key: 'pre_enrollment.email' },
              { key: 'pre_enrollment.courses' },
              { key: 'pre_enrollment.round_date' },
              { key: 'pre_enrollment.status' },
            ],
          },
        },
      });
    return resp;
  }

  async exportOICRegulatorPreReport(
    applicantType: string,
    licenseType: string,
    preEnrollmentStatus: string,
    roundDateFrom: Date,
    roundDateTo: Date,
    trainingCenter: string,
    reportType?: string | '',
    licenseRenewal?: string | '',
    withToken = true,
  ): Promise<APIResponse> {
    const resp = await this.contextManager
      .getContext()
      .post(`${this.baseApiUrl}${this.endpointReportsV1}${this.endpointExportOICRegulatorPreReport}`, {
        headers: this.getHeaderForBaseUrl(withToken),
        data: {
          applicantType: applicantType,
          licenseRenewal: licenseRenewal ?? '',
          licenseType: licenseType,
          preEnrollmentStatus: preEnrollmentStatus,
          reportType: reportType ?? '',
          roundDateFrom: roundDateFrom,
          roundDateTo: roundDateTo,
          trainingCenter: trainingCenter,
        },
      });

    return resp;
  }

  async exportOICRegulatorPostReport(
    applicantType: string,
    licenseType: string,
    preEnrollmentStatus: string,
    roundDateFrom: Date,
    roundDateTo: Date,
    trainingCenter: string,
    reportType?: string | '',
    licenseRenewal?: string | '',
    withToken = true,
  ): Promise<APIResponse> {
    const resp = await this.contextManager
      .getContext()
      .post(`${this.baseApiUrl}${this.endpointReportsV1}${this.endpointExportOICRegulatorPostReport}`, {
        headers: this.getHeaderForBaseUrl(withToken),
        data: {
          applicantType: applicantType,
          licenseRenewal: licenseRenewal ?? '',
          licenseType: licenseType,
          preEnrollmentStatus: preEnrollmentStatus,
          reportType: reportType ?? '',
          roundDateFrom: roundDateFrom,
          roundDateTo: roundDateTo,
          trainingCenter: trainingCenter,
        },
      });

    return resp;
  }

  async getReportHistoriesList(withToken = true): Promise<APIResponse> {
    const resp = await this.contextManager
      .getContext()
      .get(`${this.baseApiUrl}${this.endpointReportHistoriesV1}${this.endpointReportHistories}`, {
        headers: this.getHeaderForBaseUrl(withToken),
      });
    return resp;
  }

  async exportLearnerReport(
    categories: string[] | [],
    courses: string[] | [],
    endDateRange: Date[],
    enrollmentStatus: string[] | [],
    evaluateResult: string[] | [],
    modelContentUsageTypes: string[] | [],
    objectiveType: string[] | [],
    packageIds: string[] | [],
    planIds: string[] | [],
    regulators: string[] | [],
    startDateRange: Date[],
    withToken = true,
  ): Promise<APIResponse> {
    const resp = await this.contextManager
      .getContext()
      .post(`${this.baseApiUrl}${this.endpointReportsV1}${this.endpointExportLearnerReport}`, {
        headers: this.getHeaderForBaseUrl(withToken),
        data: {
          categories: categories,
          courses: courses,
          endDateRange: endDateRange,
          enrollmentStatus: enrollmentStatus,
          evaluateResult: evaluateResult,
          modelContentUsageTypes: modelContentUsageTypes,
          objectiveType: objectiveType,
          packageIds: packageIds,
          planIds: planIds,
          regulators: regulators,
          startDateRange: startDateRange,
        },
      });

    return resp;
  }

  async exportReport(
    id: string,
    month: number | '',
    year: string | '',
    dateRange?: string | '',
    endDateRange?: string | '',
    startDateRange?: string | '',
    withToken = true,
  ): Promise<APIResponse> {
    const resp = await this.contextManager
      .getContext()
      .post(
        `${this.baseApiUrl}${this.endpointOrganizationReportV1}${this.endpointOrganizationReport}/${id}/export-report`,
        {
          headers: this.getHeaderForBaseUrl(withToken),
          data: {
            month: month ?? '',
            year: year ?? '',
            dateRange: dateRange ?? '',
            endDateRange: endDateRange ?? '',
            startDateRange: startDateRange ?? '',
          },
        },
      );

    return resp;
  }

  async exportSurveySubmissionReport(
    courseIds: string | null,
    enrollmentStatus: string | null,
    materialMediaId: string,
    submissionDateFrom: Date,
    submissionDateTo: Date,
    withToken = true,
  ): Promise<APIResponse> {
    const resp = await this.contextManager
      .getContext()
      .post(`${this.baseApiUrl}${this.endpointOrganizationReportV1}/export-survey-submission-report`, {
        headers: this.getHeaderForBaseUrl(withToken),
        data: {
          courseIds: courseIds ?? null,
          enrollmentStatus: enrollmentStatus ?? null,
          materialMediaId: materialMediaId,
          submissionDateFrom: submissionDateFrom,
          submissionDateTo: submissionDateTo,
        },
      });

    return resp;
  }

  async exportKnowledgeContentReport(
    id: string,
    createDateRange: string,
    publishDateRange: string,
    withToken = true,
  ): Promise<APIResponse> {
    const resp = await this.contextManager
      .getContext()
      .post(
        `${this.baseApiUrl}${this.endpointOrganizationReportV1}${this.endpointOrganizationReport}/${id}/export-report`,
        {
          headers: this.getHeaderForBaseUrl(withToken),
          data: {
            createDateRange: createDateRange,
            publishDateRange: publishDateRange,
          },
        },
      );

    return resp;
  }

  async getReportList(menuKey: string, withToken = true): Promise<APIResponse> {
    const resp = await this.contextManager
      .getContext()
      .get(
        `${this.baseApiUrl}${this.endpointOrganizationReportV1}${this.endpointOrganizationReport}?menuKey=${menuKey}`,
        {
          headers: this.getHeaderForBaseUrl(withToken),
        },
      );
    return resp;
  }
}
